// tsconfig.json
{
  // Inherits the base configuration recommended by Expo for React Native projects.
  "extends": "expo/tsconfig.base",

  "compilerOptions": {
    // Enforces a wide range of type-checking rules to catch common errors.
    "strict": true,

    // --- Absolute Imports Configuration (The Correct Version) ---
    // Sets the base directory for module resolution to the project root.
    "baseUrl": ".",
    // Defines that any import starting with "@/" should be resolved from the "src/" directory.
    // This is the correct mapping for your project structure.
    "paths": {
      "@/*": ["src/*"]
    }
  },

  // --- File Inclusion Rules ---
  // This tells TypeScript which files and folders to include in the compilation process.
  // It's crucial for making sure all your code and type definitions are recognized.
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts",
    "nativewind-env.d.ts"
  ]
}