# React Native + Expo Go App Structure v1 (DRY, KISS, YAGNI)

React Native + Expo Go app + Clerk Auth

I will user Clerk for Auth, React Native Paper for Elements, NativeWind for Styling and styles/material-theme.json for css exported from Material Generator

I need pages:

welcome
sign-in
sign-up

Filenames are kebab-case

Requirement 1: React Native Paper Integration with Material Design 3

Requirement 2: Enhanced Visual Effects with Paper Components

Requirement 3 : Material Design 3 Theme System

Requirement 4: Internationalization Support with namespaces for featured based areas

ID Requirement

UR1 The system shall provide a centralized theme for the entire application.

UR2 The system shall use the material-theme.json file as the single source of truth for the theme colors.

UR3 The system shall support both light and dark color schemes.

ED1 When the user's device color scheme changes, the system shall automatically switch between the light and dark themes.

OP1 Where react-native-paper components are used, the system shall apply the centralized theme.

OP2 Where Tailwind CSS is used, the system shall provide access to the centralized theme's colors.



## Project Root Structure with Featured Based and Clear Separation of Concern even tough following a DRY, KISS and YAGNI concepts.

```
src/
├── app/                         # Expo Router
│   ├── (auth)/
│   │   ├── _layout.tsx
│   │   ├── welcome.tsx
│   │   ├── sign-in.tsx
│   │   └── sign-up.tsx
│   ├── _layout.tsx              # Root layout with providers
│   └── index.tsx                # Home/Dashboard
│   ├── (tabs)/                  # Main app tabs
│   │   ├── _layout.tsx          # Tab navigator
│   │   └── index.tsx            # Home tab
├── components/                   # Shared UI components
│   ├── ui/                      # Base UI components
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── card.tsx
│   │   └── index.ts
│   ├── layout/                  # Layout components
│   │   ├── screen-wrapper.tsx
│   │   ├── safe-area-wrapper.tsx
│   │   └── index.ts
│   └── index.ts
├── features/                    # Feature-based modules
│   ├── auth/                   # Authentication feature
│   │   ├── components/         # Auth-specific components
│   │   │   ├── auth-form.tsx
│   │   │   ├── social-login.tsx
│   │   │   └── index.ts
│   │   ├── hooks/              # Auth hooks
│   │   │   ├── use-auth.ts
│   │   │   └── index.ts
│   │   ├── services/           # Auth services
│   │   │   ├── auth-service.ts
│   │   │   └── index.ts
│   │   ├── types/              # Auth types
│   │   │   └── index.ts
│   │   └── index.ts
├── shared/                     # Shared utilities and services
│   ├── hooks/                  # Global hooks
│   │   ├── use-theme.ts
│   │   ├── use-storage.ts
│   │   └── index.ts
│   ├── services/               # Global services
│   │   ├── api/
│   │   │   ├── client.ts
│   │   │   └── index.ts
│   │   └── storage/
│   │       ├── secure-storage.ts
│   │       └── index.ts
│   ├── utils/                  # Utility functions
│   │   ├── validation.ts
│   │   ├── format.ts
│   │   └── index.ts
│   ├── constants/              # App constants
│   │   ├── routes.ts
│   │   ├── config.ts
│   │   └── index.ts
│   └── types/                  # Global types
│       ├── navigation.ts
│       ├── theme.ts
│       └── index.ts
├── styles/
│   ├── material-theme.json
│   └── global.css              # Global styles and Tailwind imports
├── locales/                    # Internationalization
│   ├── en/                     # English translations
│   │   ├── common.json         # Common translations
│   │   ├── auth.json           # Auth feature translations
│   │   ├── onboarding.json     # Onboarding translations
│   │   └── index.ts
│   ├── pt/                     # Portuguese translations
│   │   ├── common.json
│   │   ├── auth.json
│   │   ├── onboarding.json
│   │   └── index.ts
│   ├── i18n.ts                 # i18n configuration
│   └── index.ts
└── assets/                     # Static assets
    ├── images/
    ├── icons/
    └── fonts/
```

## V1 Key Simplifications

### 1. **Eliminated Over-Engineering** (YAGNI)
- ❌ Removed complex feature folders until actually needed
- ❌ Removed multiple service layers 
- ❌ Removed namespace-based i18n (simple key-based instead)
- ❌ Removed separate theme provider context
- ✅ Start simple, scale when needed

### 2. **DRY Implementation**

The Change:
Add the baseUrl and paths properties to your compilerOptions.
Generated json
// tsconfig.json
// tsconfig.json
{
  // Inherits the base configuration recommended by Expo for React Native projects.
  "extends": "expo/tsconfig.base",

  "compilerOptions": {
    // Enforces a wide range of type-checking rules to catch common errors.
    "strict": true,

    // --- Absolute Imports Configuration (The Correct Version) ---
    // Sets the base directory for module resolution to the project root.
    "baseUrl": ".",
    // Defines that any import starting with "@/" should be resolved from the "src/" directory.
    // This is the correct mapping for your project structure.
    "paths": {
      "@/*": ["src/*"]
    }
  },

  // --- File Inclusion Rules ---
  // This tells TypeScript which files and folders to include in the compilation process.
  // It's crucial for making sure all your code and type definitions are recognized.
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts",
    "nativewind-env.d.ts"
  ]
}
Use code with caution.
Json
After making this change, you may need to restart your development server and your code editor's TypeScript server for it to take effect.
File to Change 2: All files with ../ imports
The Goal: Replace all messy relative imports with clean absolute ones.
src/app/_layout.tsx
Generated typescript
// BEFORE:
import { useAppTheme } from '../shared/hooks/use-theme';
import '../locales/i18n';
import '../styles/global.css';

// AFTER (Clean Absolute Imports):
import { useAppTheme } from '@/shared/hooks/use-theme';
import '@/locales/i18n';
import '@/styles/global.css';

#### Single Theme Configuration
```typescript
// src/shared/hooks/use-theme.ts - ONE file for all theme logic
import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import materialColors from '../assets/material-theme.json';

// Single theme object that works for both Paper and Tailwind
export const createTheme = (isDark: boolean) => ({
  ...( isDark ? MD3DarkTheme : MD3LightTheme),
  colors: {
    ...(isDark ? MD3DarkTheme.colors : MD3LightTheme.colors),
    ...materialColors.schemes[isDark ? 'dark' : 'light'],
  },
});

// Single hook for theme access everywhere
export const useAppTheme = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  return createTheme(isDark);
};
```

#### Simplified Tailwind Config
```javascript
// tailwind.config.js - Direct theme mapping
const { schemes } = require('./src/styles/material-theme.json');

module.exports = {
  darkMode: 'class',
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      // By defining both, your IDE's Tailwind extension can provide
      // autocompletion for both light and dark theme colors.
      colors: {
        ...schemes.light,
        dark: {
          ...schemes.dark,
        },
      },
    },
  },
  plugins: [],
};
```

### 3. **KISS Architecture**

#### Root Layout (with i18n setup)
```typescript
import { ClerkProvider, useAuth } from '@clerk/clerk-expo';
import { PaperProvider } from 'react-native-paper';
import { Slot, useRouter, useSegments } from 'expo-router';
import { useEffect } from 'react';
import { useColorScheme } from 'nativewind';
import * as SplashScreen from 'expo-splash-screen';

// Import your custom theme hook, i18n setup, and global styles
import { useAppTheme } from '../shared/hooks/use-theme';
import '../locales/i18n'; // Corrected path based on your structure
import '../styles/global.css';

// Prevent the splash screen from auto-hiding before we can manage it.
SplashScreen.preventAutoHideAsync();

/**
 * This component handles the core logic for authentication, theme switching,
 * and splash screen management. It ensures that hooks are called unconditionally.
 */
const InitialLayout = () => {
  // --- 1. Call all hooks at the top level ---
  const { isLoaded, isSignedIn } = useAuth();
  const segments = useSegments();
  const router = useRouter();
  const appTheme = useAppTheme();
  const { setColorScheme } = useColorScheme();

  // --- 2. Manage side effects with useEffect ---

  // Effect for synchronizing the NativeWind theme with the device theme
  useEffect(() => {
    setColorScheme(appTheme.dark ? 'dark' : 'light');
  }, [appTheme.dark, setColorScheme]);

  // Effect for handling authentication-based routing
  useEffect(() => {
    // Wait until Clerk has loaded its authentication state
    if (!isLoaded) {
      return;
    }

    const inAuthGroup = segments[0] === '(auth)';

    // If the user is signed in but is currently in the auth flow (e.g., they pressed back),
    // redirect them to the main app.
    if (isSignedIn && inAuthGroup) {
      router.replace('/(tabs)/'); // Or your main dashboard route
    }
    // If the user is not signed in and is not in the auth flow,
    // redirect them to the welcome screen.
    else if (!isSignedIn && !inAuthGroup) {
      router.replace('/welcome');
    }
  }, [isLoaded, isSignedIn, segments, router]);

  // Effect for hiding the splash screen once everything is ready
  useEffect(() => {
    if (isLoaded) {
      SplashScreen.hideAsync();
    }
  }, [isLoaded]);

  // --- 3. Conditional return after all hooks are called ---

  // If Clerk is not loaded yet, return null. The splash screen will remain visible.
  if (!isLoaded) {
    return null;
  }

  // --- 4. Render the main content ---
  // Once loaded, wrap the current screen (represented by <Slot />) in the theme provider.
  return (
    <PaperProvider theme={appTheme}>
      <Slot />
    </PaperProvider>
  );
};

/**
 * This is the root layout for the entire application.
 * It sets up the top-level providers, like Clerk for authentication.
 */
export default function RootLayout() {
  // You can load fonts or other global assets here if needed
  // const [fontsLoaded] = useFonts({...});

  return (
    <ClerkProvider publishableKey={process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!}>
      <InitialLayout />
    </ClerkProvider>
  );
}
```

#### Single Auth Form Component (with namespaced i18n)
```typescript
// src/components/auth-form.tsx - One component, multiple modes
import { useState } from 'react';
import { Button, TextInput, Card } from 'react-native-paper';
import { useSignIn, useSignUp } from '@clerk/clerk-expo';
import { useTranslation } from '../../locales/i18n';

type AuthMode = 'sign-in' | 'sign-up';

export const AuthForm = ({ mode }: { mode: AuthMode }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  
  const { signIn } = useSignIn();
  const { signUp } = useSignUp();
  const { t } = useTranslation('auth'); // Auth namespace
  const { t: tCommon } = useTranslation('common'); // Common namespace
  
  const isSignUp = mode === 'sign-up';
  const action = isSignUp ? signUp : signIn;
  
  return (
    <Card className="m-4 p-4">
      <Card.Title title={t(isSignUp ? 'signUp.title' : 'signIn.title')} />
      <Card.Content className="gap-4">
        <TextInput
          label={tCommon('labels.email')}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
        />
        <TextInput
          label={tCommon('labels.password')}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />
        <Button 
          mode="contained" 
          onPress={() => action?.create({ emailAddress: email, password })}
        >
          {t(isSignUp ? 'signUp.submit' : 'signIn.submit')}
        </Button>
      </Card.Content>
    </Card>
  );
};
```

#### Simple Screen Wrapper
```typescript
// src/components/screen-wrapper.tsx - DRY screen layout
import { ScrollView, View } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

interface ScreenWrapperProps {
  children: React.ReactNode;
  scrollable?: boolean;
}

export const ScreenWrapper = ({ children, scrollable = false }: ScreenWrapperProps) => (
  <SafeAreaView className="flex-1 bg-background">
    {scrollable ? (
      <ScrollView className="flex-1">{children}</ScrollView>
    ) : (
      <View className="flex-1">{children}</View>
    )}
  </SafeAreaView>
);
```

### 4. **Namespace-Based I18n** (DRY + Maintainable)
```typescript
// src/localesi18n.ts - Namespace-based translation system
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import all translations
import commonEn from '../assets/locales/en/common.json';
import authEn from '../assets/locales/en/auth.json';
import onboardingEn from '../assets/locales/en/onboarding.json';

import commonEs from '../assets/locales/es/common.json';
import authEs from '../assets/locales/es/auth.json';
import onboardingEs from '../assets/locales/es/onboarding.json';

import commonPt from '../assets/locales/pt/common.json';
import authPt from '../assets/locales/pt/auth.json';
import onboardingPt from '../assets/locales/pt/onboarding.json';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { common: commonEn, auth: authEn, onboarding: onboardingEn },
      es: { common: commonEs, auth: authEs, onboarding: onboardingEs },
      pt: { common: commonPt, auth: authPt, onboarding: onboardingPt },
    },
    fallbackLng: 'en',
    defaultNS: 'common',
    ns: ['common', 'auth', 'onboarding'],
    interpolation: { escapeValue: false },
  });

export default i18n;

// Typed hook for namespace-based translations
export const useTranslation = (namespace?: string) => {
  const { t, i18n } = require('react-i18next').useTranslation(namespace);
  return { t, locale: i18n.language };
};

// Example translation files structure:
// common.json:
{
  "buttons": {
    "save": "Save",
    "cancel": "Cancel",
    "continue": "Continue"
  },
  "labels": {
    "email": "Email",
    "password": "Password"
  }
}

// auth.json:
{
  "welcome": {
    "title": "Welcome!",
    "subtitle": "Get started with your account"
  },
  "signIn": {
    "title": "Welcome Back",
    "submit": "Sign In",
    "forgotPassword": "Forgot Password?"
  },
  "signUp": {
    "title": "Create Account",
    "submit": "Sign Up",
    "hasAccount": "Already have an account?"
  }
}

// onboarding.json:
{
  "splash": {
    "loading": "Loading...",
    "welcome": "Welcome to the app"
  }
}
```

### 5. **Minimal File Structure Examples**

#### Auth Screens (DRY)
```typescript
// src/app/(auth)/sign-in.tsx
import { AuthForm } from '../../components/auth-form';
import { ScreenWrapper } from '../../components/screen-wrapper';

export default function SignInScreen() {
  return (
    <ScreenWrapper scrollable>
      <AuthForm mode="sign-in" />
    </ScreenWrapper>
  );
}

// src/app/(auth)/sign-up.tsx
import { AuthForm } from '../../components/auth-form';
import { ScreenWrapper } from '../../components/screen-wrapper';

export default function SignUpScreen() {
  return (
    <ScreenWrapper scrollable>
      <AuthForm mode="sign-up" />
    </ScreenWrapper>
  );
}
```

#### Welcome Screen (with namespaced i18n)
```typescript
// src/app/(auth)/welcome.tsx (ALREADY CREATED)

import { View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useRouter } from 'expo-router';

// Using absolute imports for a cleaner codebase
import { ScreenWrapper } from '@/components/layout/screen-wrapper';
import { useTranslation } from '@/locales/i18n';

/**
 * The WelcomeScreen is the first screen a new or logged-out user sees.
 * It provides clear calls to action for signing in or creating an account.
 */
export default function WelcomeScreen() {
  // Hook for programmatic navigation from Expo Router
  const router = useRouter();

  // Hook for internationalization, using the 'auth' namespace
  const { t } = useTranslation('auth');

  return (
    <ScreenWrapper>
      {/* Main container using NativeWind for layout and styling */}
      <View className="flex-1 justify-center items-center p-8 gap-y-6">
        
        {/* App Logo or Icon can go here */}
        {/* <YourLogoComponent /> */}

        <View className="items-center">
          <Text variant="headlineLarge" className="font-bold">
            {t('welcome.title')}
          </Text>
          <Text variant="bodyMedium" className="text-center mt-2">
            {t('welcome.subtitle')}
          </Text>
        </View>

        {/* Action Buttons Container */}
        <View className="w-full gap-y-4 mt-8">
          <Button
            mode="contained"
            onPress={() => router.push('/sign-up')}
            className="p-1" // Add some padding for a larger touch target
          >
            {t('signUp.title')}
          </Button>

          <Button
            mode="outlined"
            onPress={() => router.push('/sign-in')}
          >
            {t('signIn.title')}
          </Button>
        </View>
      </View>
    </ScreenWrapper>
  );
}
```

Create the Profile Screen File
Following your boilerplate's structure, create a new file for the user profile screen inside your (tabs) group. This screen will be protected and only visible to logged-in users.

File: src/app/(tabs)/profile.tsx

Step 2: Build the Component with Clerk Hooks and React Native Paper
Use the useUser() hook to get the logged-in user's information and the useAuth() hook to get the signOut function. Then, use your React Native Paper components to display this information beautifully.

Here is a complete, practical example you can place directly into src/app/(tabs)/profile.tsx:

TypeScript
```typescript
// src/app/(tabs)/profile.tsx

import { View } from 'react-native';
import { Avatar, Button, Divider, List, Text } from 'react-native-paper';
import { useAuth, useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';

// Using absolute imports from your tsconfig.json setup
import { ScreenWrapper } from '@/components/layout/screen-wrapper';
import { useAppTheme } from '@/shared/hooks/use-theme';

export default function ProfileScreen() {
  // Clerk hooks to get user data and sign-out functionality
  const { user } = useUser();
  const { signOut } = useAuth();
  const router = useRouter();

  // Your custom theme hook
  const theme = useAppTheme();

  // Function to handle the sign-out process
  const handleSignOut = async () => {
    await signOut();
    // After signing out, you might want to redirect the user.
    // The root layout's useEffect will handle redirecting to the auth flow.
  };

  // Fallback while user data is loading
  if (!user) {
    return (
      <ScreenWrapper>
        <View className="flex-1 justify-center items-center">
          <Text>Loading profile...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  // Get the first initial for the Avatar fallback
  const userInitial = user.firstName ? user.firstName.charAt(0).toUpperCase() : '?';

  return (
    <ScreenWrapper scrollable>
      <View className="flex-1 p-6 items-center gap-y-5">
        {/* User Avatar and Name */}
        <Avatar.Text
          size={80}
          label={userInitial}
          style={{ backgroundColor: theme.colors.primaryContainer }}
          labelStyle={{ color: theme.colors.onPrimaryContainer }}
        />
        <View className="items-center">
          <Text variant="headlineMedium">
            {user.fullName ?? 'App User'}
          </Text>
          <Text variant="bodyLarge" style={{ color: theme.colors.secondary }}>
            {user.primaryEmailAddress?.emailAddress}
          </Text>
        </View>
      </View>

      <Divider />

      {/* User Information List */}
      <List.Section>
        <List.Subheader>Account Information</List.Subheader>
        <List.Item
          title="Full Name"
          description={user.fullName ?? 'Not set'}
          left={(props) => <List.Icon {...props} icon="account" />}
        />
        <List.Item
          title="Email"
          description={user.primaryEmailAddress?.emailAddress}
          left={(props) => <List.Icon {...props} icon="email" />}
        />
         <List.Item
          title="Joined"
          description={user.createdAt?.toLocaleDateString()}
          left={(props) => <List.Icon {...props} icon="calendar-check" />}
        />
      </List.Section>

      <Divider />

      {/* Sign Out Button */}
      <View className="p-6 mt-4">
        <Button
          mode="contained"
          icon="logout"
          onPress={handleSignOut}
          textColor={theme.colors.onError} // For a destructive action look
          buttonColor={theme.colors.error}
        >
          Sign Out
        </Button>
      </View>
    </ScreenWrapper>
  );
}
```

## Please Avoid Mistakes Made by Other Devs

### ❌ Over-Engineered Parts (YAGNI Violations)
1. **Complex feature folders** - Start with simple components, refactor when you have 20+ components
2. **Multiple service layers** - Clerk handles auth, no need for custom services yet
4. **Separate theme contexts** - Built-in Paper theme + hook is sufficient
5. **Multiple utility folders** - One `lib` folder with specific files
6. **Complex state management** - Clerk + React state covers current needs

### ❌ DRY Violations Fixed
1. **Duplicate theme definitions** - Single source in `lib/theme.ts`
2. **Repeated screen layouts** - Single `ScreenWrapper` component
3. **Similar auth forms** - One component with mode prop
4. **Multiple translation setups** - Simple i18n configuration

### ❌ KISS Violations Fixed
1. **Complex provider nesting** - All providers in root layout
2. **Abstract service patterns** - Direct API calls where needed
3. **Over-abstracted hooks** - Simple, specific hooks only

## Migration Path (When to Add Complexity)

### When to add feature folders:
- **>15 components** in the same domain
- **>3 developers** working on the same area
- **Complex business logic** that needs isolation

### When to add service layers:
- **>5 different API endpoints** being called
- **Complex data transformation** needed
- **Caching/offline** requirements emerge

### When to add advanced state management:
- **>10 screens** sharing the same data
- **Complex data relationships** between entities
- **Real-time updates** needed

## Benefits of V1 Structure

1. **Faster Development**: Less boilerplate, quicker to implement features
2. **Easier Maintenance**: Fewer files to manage and update
3. **Better Performance**: Less abstraction layers, more direct code
4. **Clearer Understanding**: New developers can grasp the structure quickly
5. **Future-Proof**: Easy to refactor into more complex patterns when needed

## Current Structure Handles Your Requirements

✅ **Material Design 3 Theme**: Single theme system works for both Paper and Tailwind
✅ **Automatic theme switching**: Built into the theme hook
✅ **Clean separation**: Components, screens, and utilities are properly separated
✅ **Internationalization**: Namespace-based i18n is a strength of this boilerplate
✅ **All required screens**: Implemented with minimal code duplication

**Start simple, scale smart.** This structure gives you everything you need now, with a clear path to add complexity only when it's actually required.