{"expo": {"name": "AromaCHAT Go", "slug": "aromachat-go", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#6E56CF"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#6E56CF"}]], "experiments": {"typedRoutes": true}}}