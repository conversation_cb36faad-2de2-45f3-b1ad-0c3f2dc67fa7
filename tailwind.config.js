const { schemes } = require('./src/styles/material-theme.json');

module.exports = {
  darkMode: 'class',
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        // Light theme colors
        primary: schemes.light.primary,
        onPrimary: schemes.light.onPrimary,
        primaryContainer: schemes.light.primaryContainer,
        onPrimaryContainer: schemes.light.onPrimaryContainer,
        secondary: schemes.light.secondary,
        onSecondary: schemes.light.onSecondary,
        secondaryContainer: schemes.light.secondaryContainer,
        onSecondaryContainer: schemes.light.onSecondaryContainer,
        tertiary: schemes.light.tertiary,
        onTertiary: schemes.light.onTertiary,
        tertiaryContainer: schemes.light.tertiaryContainer,
        onTertiaryContainer: schemes.light.onTertiaryContainer,
        error: schemes.light.error,
        onError: schemes.light.onError,
        errorContainer: schemes.light.errorContainer,
        onErrorContainer: schemes.light.onErrorContainer,
        background: schemes.light.background,
        onBackground: schemes.light.onBackground,
        surface: schemes.light.surface,
        onSurface: schemes.light.onSurface,
        surfaceVariant: schemes.light.surfaceVariant,
        onSurfaceVariant: schemes.light.onSurfaceVariant,
        outline: schemes.light.outline,
        outlineVariant: schemes.light.outlineVariant,
        shadow: schemes.light.shadow,
        scrim: schemes.light.scrim,
        inverseSurface: schemes.light.inverseSurface,
        inverseOnSurface: schemes.light.inverseOnSurface,
        inversePrimary: schemes.light.inversePrimary,
      },
    },
  },
  plugins: [],
};
