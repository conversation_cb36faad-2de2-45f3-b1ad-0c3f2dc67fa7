{"name": "AromaCHAT", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "start": "EXPO_NO_TELEMETRY=1 expo start --tunnel", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@clerk/clerk-expo": "^2.14.14", "@expo/vector-icons": "^14.1.0", "expo": "^53.0.0", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-constants": "~17.1.3", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "^7.1.7", "expo-router": "~5.0.2", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "i18next": "^25.3.2", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}}