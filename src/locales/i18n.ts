import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import all translations
import { common as commonEn, auth as authEn, onboarding as onboardingEn } from './en';
import { common as commonPt, auth as authPt, onboarding as onboardingPt } from './pt';

const resources = {
  en: {
    common: commonEn,
    auth: authEn,
    onboarding: onboardingEn,
  },
  pt: {
    common: commonPt,
    auth: authPt,
    onboarding: onboardingPt,
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    defaultNS: 'common',
    ns: ['common', 'auth', 'onboarding'],
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    compatibilityJSON: 'v4', // For React Native compatibility
  });

export default i18n;

// Re-export the useTranslation hook with proper typing
export { useTranslation } from 'react-i18next';
