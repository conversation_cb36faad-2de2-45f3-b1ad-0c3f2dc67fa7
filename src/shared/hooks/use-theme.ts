import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import materialColors from '@/styles/material-theme.json';

/**
 * Creates a theme object that merges React Native Paper's base theme
 * with our custom Material Design 3 colors from material-theme.json
 */
export const createTheme = (isDark: boolean) => {
  const baseTheme = isDark ? MD3DarkTheme : MD3LightTheme;
  const colorScheme = isDark ? materialColors.schemes.dark : materialColors.schemes.light;

  return {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      ...colorScheme,
    },
  };
};

/**
 * Custom hook that provides the current theme based on the device's color scheme.
 * This is the single source of truth for theming throughout the app.
 */
export const useAppTheme = () => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  return createTheme(isDark);
};
