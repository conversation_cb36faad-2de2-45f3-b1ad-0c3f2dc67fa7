import { Stack } from 'expo-router';

/**
 * Layout for authentication screens (welcome, sign-in, sign-up).
 * This provides a simple stack navigation without tabs.
 */
export default function AuthLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false, // We'll handle our own headers in the screens
      }}
    >
      <Stack.Screen name="welcome" />
      <Stack.Screen name="sign-in" />
      <Stack.Screen name="sign-up" />
    </Stack>
  );
}
