import { View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useRouter } from 'expo-router';

// Using absolute imports for a cleaner codebase
import { ScreenWrapper } from '@/components/layout/screen-wrapper';
import { useTranslation } from '@/locales/i18n';

/**
 * The WelcomeScreen is the first screen a new or logged-out user sees.
 * It provides clear calls to action for signing in or creating an account.
 */
export default function WelcomeScreen() {
  // Hook for programmatic navigation from Expo Router
  const router = useRouter();

  // Hook for internationalization, using the 'auth' namespace
  const { t } = useTranslation('auth');

  return (
    <ScreenWrapper>
      <View className="flex-1 justify-center items-center p-6">
        {/* Welcome Content */}
        <View className="items-center mb-12">
          <Text variant="displaySmall" className="text-center mb-4 text-primary">
            {t('welcome.title')}
          </Text>
          <Text variant="bodyLarge" className="text-center text-onSurfaceVariant max-w-sm">
            {t('welcome.subtitle')}
          </Text>
        </View>

        {/* Action Buttons */}
        <View className="w-full max-w-sm gap-3">
          <Button
            mode="contained"
            onPress={() => router.push('/sign-up')}
            className="py-2"
          >
            {t('welcome.getStarted')}
          </Button>
          
          <Button
            mode="outlined"
            onPress={() => router.push('/sign-in')}
            className="py-2"
          >
            {t('signIn.title')}
          </Button>
        </View>
      </View>
    </ScreenWrapper>
  );
}
