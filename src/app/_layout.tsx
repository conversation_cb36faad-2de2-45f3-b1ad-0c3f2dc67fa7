import { Clerk<PERSON>rovider, useAuth } from '@clerk/clerk-expo';
import { PaperProvider } from 'react-native-paper';
import { Slot, useRouter, useSegments } from 'expo-router';
import { useEffect } from 'react';
import { useColorScheme } from 'nativewind';
import * as SplashScreen from 'expo-splash-screen';

// Import your custom theme hook, i18n setup, and global styles
import { useAppTheme } from '@/shared/hooks/use-theme';
import '@/locales/i18n'; // Initialize i18n
import '@/styles/global.css';

// Prevent the splash screen from auto-hiding before we can manage it.
SplashScreen.preventAutoHideAsync();

/**
 * This component handles the core logic for authentication, theme switching,
 * and splash screen management. It ensures that hooks are called unconditionally.
 */
const InitialLayout = () => {
  // --- 1. Call all hooks at the top level ---
  const { isLoaded, isSignedIn } = useAuth();
  const segments = useSegments();
  const router = useRouter();
  const appTheme = useAppTheme();
  const { setColorScheme } = useColorScheme();

  // --- 2. Manage side effects with useEffect ---

  // Effect for synchronizing the NativeWind theme with the device theme
  useEffect(() => {
    setColorScheme(appTheme.dark ? 'dark' : 'light');
  }, [appTheme.dark, setColorScheme]);

  // Effect for handling authentication-based routing
  useEffect(() => {
    // Wait until Clerk has loaded its authentication state
    if (!isLoaded) {
      return;
    }

    const inAuthGroup = segments[0] === '(auth)';

    // If the user is signed in but is currently in the auth flow (e.g., they pressed back),
    // redirect them to the main app.
    if (isSignedIn && inAuthGroup) {
      router.replace('/(tabs)/'); // Or your main dashboard route
    }
    // If the user is not signed in and is not in the auth flow,
    // redirect them to the welcome screen.
    else if (!isSignedIn && !inAuthGroup) {
      router.replace('/welcome');
    }
  }, [isLoaded, isSignedIn, segments, router]);

  // Effect for hiding the splash screen once everything is ready
  useEffect(() => {
    if (isLoaded) {
      SplashScreen.hideAsync();
    }
  }, [isLoaded]);

  // --- 3. Conditional return after all hooks are called ---

  // If Clerk is not loaded yet, return null. The splash screen will remain visible.
  if (!isLoaded) {
    return null;
  }

  // --- 4. Render the main content ---
  // Once loaded, wrap the current screen (represented by <Slot />) in the theme provider.
  return (
    <PaperProvider theme={appTheme}>
      <Slot />
    </PaperProvider>
  );
};

/**
 * This is the root layout for the entire application.
 * It sets up the top-level providers, like Clerk for authentication.
 */
export default function RootLayout() {
  // You can load fonts or other global assets here if needed
  // const [fontsLoaded] = useFonts({...});

  return (
    <ClerkProvider publishableKey={process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!}>
      <InitialLayout />
    </ClerkProvider>
  );
}
