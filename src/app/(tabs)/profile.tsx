import { View, Alert } from 'react-native';
import { <PERSON><PERSON>, Button, Divider, List, Text, Card } from 'react-native-paper';
import { useAuth, useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';

// Using absolute imports from your tsconfig.json setup
import { ScreenWrapper } from '@/components/layout/screen-wrapper';
import { useAppTheme } from '@/shared/hooks/use-theme';
import { useTranslation } from '@/locales/i18n';

/**
 * Profile Screen - Displays user information and provides sign-out functionality.
 */
export default function ProfileScreen() {
  // Clerk hooks to get user data and sign-out functionality
  const { user } = useUser();
  const { signOut } = useAuth();
  const router = useRouter();

  // Your custom theme hook
  const theme = useAppTheme();
  const { t } = useTranslation('auth');

  // Function to handle the sign-out process
  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await signOut();
            // The root layout's useEffect will handle redirecting to the auth flow.
          },
        },
      ]
    );
  };

  // Fallback while user data is loading
  if (!user) {
    return (
      <ScreenWrapper>
        <View className="flex-1 justify-center items-center">
          <Text>Loading profile...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  // Get the first initial for the Avatar fallback
  const userInitial = user.firstName ? user.firstName.charAt(0).toUpperCase() : '?';
  const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
  const displayName = fullName || user.emailAddresses[0]?.emailAddress || 'User';

  return (
    <ScreenWrapper scrollable>
      <View className="p-4">
        {/* Profile Header */}
        <Card className="mb-6">
          <Card.Content>
            <View className="items-center py-4">
              <Avatar.Text 
                size={80} 
                label={userInitial}
                style={{ backgroundColor: theme.colors.primary }}
              />
              <Text variant="titleLarge" className="mt-3 text-center">
                {displayName}
              </Text>
              <Text variant="bodyMedium" className="text-onSurfaceVariant text-center">
                {user.emailAddresses[0]?.emailAddress}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Profile Options */}
        <Card className="mb-6">
          <Card.Content>
            <Text variant="titleMedium" className="mb-3">
              {t('profile.title')} Options
            </Text>
            
            <List.Item
              title={t('profile.editProfile')}
              left={(props) => <List.Icon {...props} icon="account-edit" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('Coming Soon', 'Profile editing will be implemented soon.');
              }}
            />
            
            <Divider />
            
            <List.Item
              title={t('profile.accountSettings')}
              left={(props) => <List.Icon {...props} icon="cog" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('Coming Soon', 'Account settings will be implemented soon.');
              }}
            />
            
            <Divider />
            
            <List.Item
              title={t('profile.preferences')}
              left={(props) => <List.Icon {...props} icon="tune" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('Coming Soon', 'Preferences will be implemented soon.');
              }}
            />
          </Card.Content>
        </Card>

        {/* App Info */}
        <Card className="mb-6">
          <Card.Content>
            <Text variant="titleMedium" className="mb-3">
              {t('profile.about')}
            </Text>
            
            <List.Item
              title="AromaCHAT Go"
              description="Version 1.0.0"
              left={(props) => <List.Icon {...props} icon="information" />}
            />
            
            <List.Item
              title="Built with React Native + Expo"
              description="Material Design 3, Clerk Auth, NativeWind"
              left={(props) => <List.Icon {...props} icon="react" />}
            />
          </Card.Content>
        </Card>

        {/* Sign Out Button */}
        <Button
          mode="contained"
          onPress={handleSignOut}
          buttonColor={theme.colors.error}
          textColor={theme.colors.onError}
          className="mb-4"
        >
          {t('profile.signOut')}
        </Button>
      </View>
    </ScreenWrapper>
  );
}
