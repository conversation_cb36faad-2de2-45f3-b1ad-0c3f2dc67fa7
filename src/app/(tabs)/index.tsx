import { View } from 'react-native';
import { Text, Card } from 'react-native-paper';
import { useUser } from '@clerk/clerk-expo';

// Using absolute imports from your tsconfig.json setup
import { ScreenWrapper } from '@/components/layout/screen-wrapper';
import { useTranslation } from '@/locales/i18n';

/**
 * Home Screen - The main dashboard for logged-in users.
 */
export default function HomeScreen() {
  const { user } = useUser();
  const { t } = useTranslation('auth');

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <ScreenWrapper scrollable>
      <View className="p-4">
        {/* Header */}
        <View className="mb-6">
          <Text variant="headlineMedium" className="text-primary">
            {getGreeting()}
          </Text>
          {user && (
            <Text variant="bodyLarge" className="text-onSurfaceVariant">
              {user.firstName || user.emailAddresses[0]?.emailAddress}
            </Text>
          )}
        </View>

        {/* Welcome Card */}
        <Card className="mb-6">
          <Card.Content>
            <Text variant="titleLarge" className="mb-2">
              Welcome to AromaCHAT Go! 🎉
            </Text>
            <Text variant="bodyMedium" className="text-onSurfaceVariant">
              Your React Native app with Expo, Clerk authentication, React Native Paper, 
              and NativeWind is ready to go. This boilerplate follows DRY, KISS, and YAGNI principles 
              to keep your code clean and maintainable.
            </Text>
          </Card.Content>
        </Card>

        {/* Features Card */}
        <Card>
          <Card.Content>
            <Text variant="titleMedium" className="mb-3">
              🚀 Features Included:
            </Text>
            <View className="gap-2">
              <Text variant="bodyMedium">• 🎨 Material Design 3 theming</Text>
              <Text variant="bodyMedium">• 🌓 Automatic dark/light mode</Text>
              <Text variant="bodyMedium">• 🔐 Clerk authentication</Text>
              <Text variant="bodyMedium">• 🌍 Internationalization (i18n)</Text>
              <Text variant="bodyMedium">• 📱 React Native Paper components</Text>
              <Text variant="bodyMedium">• 💨 NativeWind styling</Text>
              <Text variant="bodyMedium">• 📂 Absolute imports (@/*)</Text>
              <Text variant="bodyMedium">• 🛡️ TypeScript with strict mode</Text>
            </View>
          </Card.Content>
        </Card>
      </View>
    </ScreenWrapper>
  );
}
