import { ScrollView, View } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

interface ScreenWrapperProps {
  children: React.ReactNode;
  scrollable?: boolean;
  className?: string;
}

/**
 * A wrapper component that provides consistent screen layout with safe area handling.
 * Use this component to wrap all your screen content for consistent spacing and behavior.
 */
export const ScreenWrapper = ({ 
  children, 
  scrollable = false, 
  className = '' 
}: ScreenWrapperProps) => (
  <SafeAreaView className={`flex-1 bg-background ${className}`}>
    {scrollable ? (
      <ScrollView 
        className="flex-1" 
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
      >
        {children}
      </ScrollView>
    ) : (
      <View className="flex-1">{children}</View>
    )}
  </SafeAreaView>
);
