import { useState } from 'react';
import { View, Alert } from 'react-native';
import { Button, TextInput, Card, Text, Divider } from 'react-native-paper';
import { useSignIn, useSignUp } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { useTranslation } from '@/locales/i18n';

type AuthMode = 'sign-in' | 'sign-up';

interface AuthFormProps {
  mode: AuthMode;
}

/**
 * A unified form component that handles both sign-in and sign-up flows.
 * This follows the DRY principle by reusing form logic and UI for both auth modes.
 */
export const AuthForm = ({ mode }: AuthFormProps) => {
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Hooks
  const { signIn, setActive } = useSignIn();
  const { signUp, setActive: setActiveSignUp } = useSignUp();
  const router = useRouter();
  const { t } = useTranslation('auth');
  const { t: tCommon } = useTranslation('common');

  const isSignUp = mode === 'sign-up';

  // Handle form submission
  const handleSubmit = async () => {
    if (!email || !password || (isSignUp && (!firstName || !lastName))) {
      Alert.alert(
        tCommon('labels.error'),
        tCommon('messages.fieldRequired')
      );
      return;
    }

    setIsLoading(true);

    try {
      if (isSignUp) {
        // Sign up flow
        const result = await signUp?.create({
          emailAddress: email,
          password,
          firstName,
          lastName,
        });

        if (result?.status === 'complete') {
          await setActiveSignUp?.({ session: result.createdSessionId });
          router.replace('/(tabs)/');
        }
      } else {
        // Sign in flow
        const result = await signIn?.create({
          identifier: email,
          password,
        });

        if (result?.status === 'complete') {
          await setActive?.({ session: result.createdSessionId });
          router.replace('/(tabs)/');
        }
      }
    } catch (err: any) {
      Alert.alert(
        tCommon('labels.error'),
        err?.errors?.[0]?.message || tCommon('messages.somethingWentWrong')
      );
    } finally {
      setIsLoading(false);
    }
  };

  const navigateToAlternateMode = () => {
    router.push(isSignUp ? '/sign-in' : '/sign-up');
  };

  return (
    <Card className="m-4 p-4">
      <Card.Content>
        {/* Header */}
        <View className="mb-6">
          <Text variant="headlineMedium" className="text-center mb-2">
            {t(isSignUp ? 'signUp.title' : 'signIn.title')}
          </Text>
          <Text variant="bodyMedium" className="text-center text-onSurfaceVariant">
            {t(isSignUp ? 'signUp.subtitle' : 'signIn.subtitle')}
          </Text>
        </View>

        {/* Form Fields */}
        <View className="gap-4">
          {isSignUp && (
            <>
              <TextInput
                label={tCommon('labels.firstName')}
                value={firstName}
                onChangeText={setFirstName}
                mode="outlined"
                placeholder={t('signUp.firstNamePlaceholder')}
                autoCapitalize="words"
                textContentType="givenName"
              />
              <TextInput
                label={tCommon('labels.lastName')}
                value={lastName}
                onChangeText={setLastName}
                mode="outlined"
                placeholder={t('signUp.lastNamePlaceholder')}
                autoCapitalize="words"
                textContentType="familyName"
              />
            </>
          )}
          
          <TextInput
            label={tCommon('labels.email')}
            value={email}
            onChangeText={setEmail}
            mode="outlined"
            placeholder={t(isSignUp ? 'signUp.emailPlaceholder' : 'signIn.emailPlaceholder')}
            keyboardType="email-address"
            autoCapitalize="none"
            textContentType="emailAddress"
          />
          
          <TextInput
            label={tCommon('labels.password')}
            value={password}
            onChangeText={setPassword}
            mode="outlined"
            placeholder={t(isSignUp ? 'signUp.passwordPlaceholder' : 'signIn.passwordPlaceholder')}
            secureTextEntry
            textContentType={isSignUp ? 'newPassword' : 'password'}
          />
        </View>

        {/* Submit Button */}
        <Button
          mode="contained"
          onPress={handleSubmit}
          loading={isLoading}
          disabled={isLoading}
          className="mt-6"
        >
          {t(isSignUp ? 'signUp.submit' : 'signIn.submit')}
        </Button>

        {/* Forgot Password (Sign In only) */}
        {!isSignUp && (
          <Button
            mode="text"
            onPress={() => {
              // TODO: Implement forgot password
              Alert.alert('Coming Soon', 'Forgot password functionality will be implemented soon.');
            }}
            className="mt-2"
          >
            {t('signIn.forgotPassword')}
          </Button>
        )}

        {/* Divider */}
        <Divider className="my-6" />

        {/* Alternative Action */}
        <View className="flex-row justify-center items-center">
          <Text variant="bodyMedium" className="text-onSurfaceVariant">
            {t(isSignUp ? 'signUp.hasAccount' : 'signIn.noAccount')}{' '}
          </Text>
          <Button mode="text" onPress={navigateToAlternateMode}>
            {t(isSignUp ? 'signUp.signInLink' : 'signIn.signUpLink')}
          </Button>
        </View>
      </Card.Content>
    </Card>
  );
};
