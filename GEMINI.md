# Project: AromaCHAT Go

## Project Overview

AromaCHAT Go is a production-ready boilerplate for building high-quality React Native applications using Expo. It is built on a foundation of modern tools and best practices, with a strong emphasis on the principles of **DRY** (Don't Repeat Yourself), **KISS** (Keep It Simple, Stupid), and **YAGNI** (You Ain't Gonna Need It).

This boilerplate provides a solid, scalable, and maintainable architecture that solves common development challenges like theming, authentication, and code organization right out of the box.

## Core Features

*   **Centralized Theming System:** A single `material-theme.json` file acts as the source of truth for colors, feeding both React Native Paper and NativeWind to ensure perfect UI consistency.
*   **Automatic Dark/Light Mode:** The application theme automatically syncs with the user's device settings.
*   **Protected Routes & Auth Flow:** Uses Clerk to manage authentication, with a pre-configured root layout that handles loading states, splash screens, and automatically redirects users based on their sign-in status.
*   **Scalable File Structure:** A clean, feature-based directory structure that starts simple but is ready to scale as your application grows.
*   **Internationalization (i18n):** A pre-configured `i18next` setup with support for namespaces, making it easy to manage translations for different features.
*   **Absolute Imports:** Configured with TypeScript path aliases (`@/*`) for cleaner, more maintainable import statements.

## Tech Stack

*   **Framework:** React Native with Expo SDK 51
*   **Language:** TypeScript (with strict mode)
*   **Routing:** Expo Router
*   **Authentication:** Clerk
*   **UI Components:** React Native Paper (v5, with MD3)
*   **Styling:** NativeWind v4 (Tailwind CSS for React Native)
*   **Internationalization:** i18next with react-i18next

## Getting Started

### Prerequisites

*   Node.js (LTS version)
*   An Expo Go account and the Expo Go app on your mobile device.
*   A Clerk account to get your Publishable Key.

### Installation & Setup

1.  **Clone the repository:**
    ```bash
    git clone <your-repo-url>
    cd AromaCHAT
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env` file in the root of the project by copying the example file:
    ```bash
    cp .env.example .env
    ```
    Now, open the `.env` file and add your Clerk Publishable Key:
    ```
    EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_YOUR_PUBLISHABLE_KEY"
    ```

## Available Scripts

*   **`npm start` or `npm run dev`**: Starts the development server with Expo. You can then scan the QR code with the Expo Go app.
*   **`npm run android`**: Starts the development server and attempts to open the app on a connected Android device or emulator.
*   **`npm run ios`**: Starts the development server and attempts to open the app on an iOS simulator.
*   **`npm run web`**: Starts the development server and opens the app in a web browser.
*   **`npm run lint`**: Lints the codebase using ESLint to check for errors and style issues.

## Development Conventions

### File Structure

The project uses a clean `src` directory to separate application code from configuration files.

*   `src/app/`: Contains all screens and routing logic for Expo Router.
*   `src/components/`: Shared, reusable, and "dumb" UI components (e.g., `Button`, `Card`).
*   `src/features/`: Contains logic, components, and hooks related to a specific business feature (e.g., `auth`).
*   `src/shared/`: Utilities, hooks, and services that can be used across the entire application.
*   `src/locales/`: Internationalization files and configuration.
*   `src/styles/`: Global styles and the `material-theme.json` file.
*   `src/assets/`: Static assets like images, icons, and fonts.

### Theming

The theming system is a core strength of this boilerplate.

*   **Single Source of Truth:** All theme colors are defined in `src/styles/material-theme.json`. This file should be generated using the [Material Theme Builder](https://m3.material.io/theme-builder).
*   **React Native Paper:** The `useAppTheme` hook in `src/shared/hooks/use-theme.ts` consumes `material-theme.json` and merges it with the base Paper theme. This is provided to the app in the root `_layout.tsx`.
*   **NativeWind:** The `tailwind.config.js` file also consumes `material-theme.json` to make all theme colors available as utility classes (e.g., `bg-primary`, `text-on-surface`). Dark mode is handled via the `darkMode: 'class'` strategy.

### Routing

*   **File-Based:** Expo Router uses the file system to define routes. A file at `app/settings.tsx` creates a `/settings` route.
*   **Layouts:** `_layout.tsx` files define shared UI and logic for a group of routes.
*   **Route Protection:** The root layout (`src/app/_layout.tsx`) contains the core logic to redirect users to the `(auth)` or `(tabs)` route group based on their sign-in status.

### Internationalization (i18n)

*   **Namespaces:** Translations are organized by feature into different JSON files (e.g., `auth.json`, `common.json`) inside the `src/locales/` directory. This keeps translation files small and manageable.
*   **Usage:** Use the `useTranslation` hook to access translations.
    ```tsx
    import { useTranslation } from '@/locales/i18n';
    const { t } = useTranslation('auth'); // Specify the namespace
    // ...
    <Text>{t('signIn.title')}</Text>
    ```

### Code Quality

*   **TypeScript:** The project uses TypeScript with `strict` mode enabled to ensure type safety.
*   **Absolute Imports:** Use `@/` to import files from the `src` directory, avoiding messy relative paths like `../../`.
*   **Linting:** Run `npm run lint` to check for code quality issues.
*   **Formatting:** The project is set up with Prettier for consistent code formatting.