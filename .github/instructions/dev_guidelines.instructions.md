---
applyTo: '**'
---
# AI Developer Guidelines

## Component Usage
- Only use components from the `react-native-paper` library.
- Do not create or use custom UI components unless absolutely necessary and not available in `react-native-paper`.

## Design System
- Always use the Material Design 3 (MD3) design kit and theme setup already configured in the project.
- Do not override or bypass the existing design system.
- Focus on leveraging React Native Paper's built-in capabilities rather than reinventing styling.
- Use Card.Content instead of custom padding
- Consolidate duplicate styles - removes redundancy
- Use gap instead of individual margins

## Development Principles
- Always follow DRY (Don't Repeat Yourself), KISS (Keep It Simple, Stupid), and YAGNI (You Aren't Gonna Need It) principles.
- Avoid overengineering and unnecessary abstractions.

## Codebase Verification
- Before creating any new component, utility, or feature, search the project codebase to ensure it does not already exist.
- Reuse existing code whenever possible to maintain DRY.

## General
- Do not implement features or add code unless it is required for the current task.
- Keep solutions as simple and maintainable as possible.
